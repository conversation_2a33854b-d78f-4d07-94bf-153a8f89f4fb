@echo off
REM Fast server-only deployment script for textbook-platform

REM Configuration
set PROJECT_ID=textbook-platform
set SERVICE_NAME=my-textbook-app
set REGION=us-central1

echo 🚀 Deploying server-only to Google Cloud Run (Fast Mode)...

REM Check if gcloud is installed
where gcloud >nul 2>nul
if %ERRORLEVEL% NEQ 0 (
    echo ❌ Google Cloud CLI is not installed. Please install it first:
    echo    https://cloud.google.com/sdk/docs/install
    exit /b 1
)

REM Set the project
echo 📋 Setting project to %PROJECT_ID%...
gcloud config set project %PROJECT_ID%

REM Build and deploy using server-only Dockerfile
echo 🏗️  Building and deploying server-only container...
gcloud run deploy %SERVICE_NAME% ^
    --source . ^
    --dockerfile Dockerfile.server ^
    --platform managed ^
    --region %REGION% ^
    --allow-unauthenticated ^
    --port 8080 ^
    --memory 1Gi ^
    --cpu 1 ^
    --max-instances 10 ^
    --set-env-vars NODE_ENV=production

echo ✅ Server deployment complete!
echo.
echo 🌐 Your production URL is:
gcloud run services list --filter="metadata.name=%SERVICE_NAME%" --format="value(status.url)"
echo.
echo 📋 You can also find it at: https://console.cloud.google.com/run

// PRODUCTION FIRESTORE SECURITY RULES
// Use these rules after creating the initial admin user
rules_version = '2';
service cloud.firestore {
  match /databases/{database}/documents {
    // Users collection
    match /users/{userId} {
      // Users can read their own profile
      allow read: if request.auth != null && request.auth.uid == userId;
      
      // Only super-admins can create new users
      allow create: if request.auth != null &&
        exists(/databases/$(database)/documents/users/$(request.auth.uid)) &&
        get(/databases/$(database)/documents/users/$(request.auth.uid)).data.role == 'super-admin';
      
      // Users can update their own profile (except role)
      allow update: if request.auth != null && request.auth.uid == userId &&
        !('role' in request.resource.data.diff(resource.data).affectedKeys());
      
      // Only super-admins can update user roles
      allow update: if request.auth != null &&
        exists(/databases/$(database)/documents/users/$(request.auth.uid)) &&
        get(/databases/$(database)/documents/users/$(request.auth.uid)).data.role == 'super-admin';
      
      // Only super-admins can delete users
      allow delete: if request.auth != null &&
        exists(/databases/$(database)/documents/users/$(request.auth.uid)) &&
        get(/databases/$(database)/documents/users/$(request.auth.uid)).data.role == 'super-admin';
    }
    
    // Super-admin users can read all user profiles
    match /users/{userId} {
      allow read: if request.auth != null &&
        exists(/databases/$(database)/documents/users/$(request.auth.uid)) &&
        get(/databases/$(database)/documents/users/$(request.auth.uid)).data.role == 'super-admin';
    }
    
    // Books collection - add your existing rules here
    match /books/{bookId} {
      // Allow read access to all authenticated users
      allow read: if request.auth != null;
      
      // Allow write access to editors, admins, and super-admins
      allow write: if request.auth != null &&
        exists(/databases/$(database)/documents/users/$(request.auth.uid)) &&
        get(/databases/$(database)/documents/users/$(request.auth.uid)).data.role in ['editor', 'admin', 'super-admin'];
    }
    
    // Add other collection rules as needed
  }
}

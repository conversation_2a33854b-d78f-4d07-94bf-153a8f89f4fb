import { createRouter, createWebHistory } from 'vue-router';
import MainLayout from '../components/layout/MainLayout.vue';
import AuthLayout from '../components/layout/AuthLayout.vue';
import BookDetails from '../components/views/BookDetails.vue';
import LoginForm from '../components/auth/LoginForm.vue';
import UserManagement from '../components/UserManagement.vue';
import { useAuthStore } from '../stores/auth';

const routes = [
  {
    path: '/auth',
    component: AuthLayout,
    meta: { requiresGuest: true },
    children: [
      {
        path: 'login',
        component: LoginForm,
        meta: { requiresGuest: true }
      }
    ]
  },
  {
    path: '/login',
    redirect: '/auth/login'
  },
  {
    path: '/',
    component: MainLayout,
    meta: { requiresAuth: true },
    children: [
      {
        path: '',
        component: BookDetails,
        meta: { requiresAuth: true }
      },
      {
        path: 'book/:id',
        component: BookDetails,
        meta: { requiresAuth: true }
      },
      {
        path: 'users',
        component: UserManagement,
        meta: { requiresAuth: true, requiresSuperAdmin: true }
      }
    ]
  }
];

const router = createRouter({
  history: createWebHistory(),
  routes,
});

// Navigation guard for authentication
router.beforeEach(async (to, _from, next) => {
  const authStore = useAuthStore();

  // Wait for Firebase auth to initialize
  if (!authStore.initialized) {
    // Wait a bit for auth to initialize
    let attempts = 0;
    while (!authStore.initialized && attempts < 50) {
      await new Promise(resolve => setTimeout(resolve, 100));
      attempts++;
    }
  }

  const requiresAuth = to.matched.some(record => record.meta.requiresAuth);
  const requiresGuest = to.matched.some(record => record.meta.requiresGuest);
  const requiresSuperAdmin = to.matched.some(record => record.meta.requiresSuperAdmin);

  if (requiresAuth && !authStore.isAuthenticated) {
    // Redirect to login if authentication is required
    next('/auth/login');
  } else if (requiresGuest && authStore.isAuthenticated) {
    // Redirect to home if already authenticated and trying to access guest-only pages
    next('/');
  } else if (requiresSuperAdmin && (!authStore.user || authStore.user.role !== 'super-admin')) {
    // Redirect to home if super-admin access is required but user doesn't have it
    next('/');
  } else {
    next();
  }
});

export default router;
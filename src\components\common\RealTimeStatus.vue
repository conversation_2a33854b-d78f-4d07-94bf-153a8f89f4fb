<template>
  <div class="flex items-center gap-2 text-sm">
    <div 
      :class="[
        'w-2 h-2 rounded-full transition-colors duration-300',
        isRealTimeEnabled ? 'bg-green-500 animate-pulse' : 'bg-gray-400'
      ]"
    ></div>
    <span :class="isRealTimeEnabled ? 'text-green-600' : 'text-gray-500'">
      {{ statusText }}
    </span>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue';
import { useMainStore } from '../../stores/main';
import { useAuthStore } from '../../stores/auth';

const mainStore = useMainStore();
const authStore = useAuthStore();

const isRealTimeEnabled = computed(() => mainStore.isRealTimeEnabled);

const statusText = computed(() => {
  if (!authStore.user) {
    return 'Offline mode';
  }
  
  if (isRealTimeEnabled.value) {
    return 'Real-time updates active';
  }
  
  return 'Loading...';
});
</script>

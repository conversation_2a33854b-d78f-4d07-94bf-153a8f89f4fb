<template>
  <div class="border border-surface-300 rounded-xl p-4">
    <div class="space-y-6">
        <!-- Header with <PERSON><PERSON> Button -->
        <div class="flex justify-between items-center pb-4 border-b border-surface-200">
          <div class="flex items-center gap-3">
            <Avatar icon="pi pi-pencil" size="small" class="bg-orange-100 text-orange-600" />
            <h2 class="text-xl font-semibold text-surface-800">Edit Item</h2>
          </div>
          <Button
            icon="pi pi-times"
            size="small"
            text
            severity="secondary"
            @click="cancelEdit"
            v-tooltip="'Cancel'"
            class="transition-all duration-200"
          />
        </div>

        <!-- Basic Information Panel -->
        <Panel header="Basic Information" class="border-0 shadow-none">
          <template #header>
            <div class="flex items-center gap-2">
              <Avatar icon="pi pi-info-circle" size="small" class="bg-blue-100 text-blue-600" />
              <span class="font-semibold">Basic Information</span>
            </div>
          </template>
          <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div class="space-y-2">
                <label for="itemType" class="block text-sm font-semibold text-surface-700">Item Type</label>
                <Select
                  id="itemType"
                  v-model="editItem.type"
                  :options="itemTypes"
                  placeholder="Select Item Type"
                  class="w-full"
                />
              </div>
              <div class="space-y-2">
                <label for="itemTitle" class="block text-sm font-semibold text-surface-700">Title</label>
                <InputText
                  id="itemTitle"
                  v-model="editItem.title"
                  placeholder="Enter item title"
                  class="w-full"
                />
              </div>
            </div>
        </Panel>

        <!-- Question Type Content -->
        <Panel v-if="editItem.type === 'question'" class="border-0 shadow-none">
          <template #header>
            <div class="flex items-center gap-2">
              <Avatar icon="pi pi-question-circle" size="small" class="bg-orange-100 text-orange-600" />
              <span class="font-semibold">Question Details</span>
            </div>
          </template>
          <div class="space-y-6">
            <div class="space-y-2">
              <label class="block text-sm font-semibold text-surface-700">Question Text</label>
              <InputText
                v-model="editItem.question"
                placeholder="Enter your question"
                class="w-full"
              />
            </div>

            <div class="space-y-4">
              <div class="flex items-center justify-between">
                <label class="block text-sm font-semibold text-surface-700">Answer Options</label>
                <Button
                  label="Add Option"
                  icon="pi pi-plus"
                  size="small"
                  outlined
                  @click="addOption"
                  class="transition-all duration-200"
                />
              </div>
              <div class="space-y-3">
                <div v-for="(option, index) in editItem.options" :key="index"
                     class="flex items-center gap-3 p-4 border border-surface-200 rounded-lg hover:border-primary-300 transition-colors">
                  <RadioButton
                    :value="index"
                    v-model="editItem.correctAnswer"
                    :inputId="`edit-option-${index}`"
                  />
                  <InputText
                    v-model="option.text"
                    placeholder="Enter option text"
                    class="flex-1"
                  />
                  <Badge
                    v-if="index === editItem.correctAnswer"
                    value="Correct"
                    severity="success"
                    class="ml-2"
                  />
                  <Button
                    icon="pi pi-trash"
                    size="small"
                    text
                    severity="danger"
                    @click="removeOption(index)"
                    v-tooltip="'Remove option'"
                    class="transition-all duration-200"
                  />
                </div>
              </div>
            </div>
          </div>
        </Panel>

    <!-- Text Type -->
    <div v-else-if="editItem.type === 'text'" class="p-field">
      <label class="block text-sm font-medium text-surface-700 mb-1">Text Content</label>
      <Textarea
        v-model="editItem.content"
        placeholder="Enter your text content"
        rows="4"
        class="w-full resize-none"
      />
    </div>

    <!-- Image Type -->
    <div v-else-if="editItem.type === 'image'" class="p-field">
      <label class="block text-sm font-medium text-surface-700 mb-1">Image Upload</label>
      <div class="border-2 border-dashed border-surface-300 rounded-lg p-4 text-center">
        <FileUpload mode="basic" accept="image/*" disabled class="mb-2" />
        <small class="text-surface-500">Image upload preview only (no backend)</small>
      </div>
    </div>

    <!-- Link Type -->
    <div v-else-if="editItem.type === 'link'" class="p-field">
      <label class="block text-sm font-medium text-surface-700 mb-1">URL</label>
      <InputText
        v-model="editItem.url"
        placeholder="https://example.com"
        class="w-full"
      />
    </div>

    <!-- Map Type -->
    <div v-else-if="editItem.type === 'map'" class="p-field">
      <label class="block text-sm font-medium text-surface-700 mb-1">Map Image Upload</label>
      <div class="border-2 border-dashed border-surface-300 rounded-lg p-4 text-center">
        <FileUpload mode="basic" accept="image/*" disabled class="mb-2" />
        <small class="text-surface-500">Map image upload preview only (no backend)</small>
      </div>
    </div>

    <!-- Diagram Type -->
    <div v-else-if="editItem.type === 'diagram'" class="p-field">
      <label class="block text-sm font-medium text-surface-700 mb-1">Diagram Image Upload</label>
      <div class="border-2 border-dashed border-surface-300 rounded-lg p-4 text-center">
        <FileUpload mode="basic" accept="image/*" disabled class="mb-2" />
        <small class="text-surface-500">Diagram image upload preview only (no backend)</small>
      </div>
    </div>

    <!-- Timed Question Type -->
    <div v-else-if="editItem.type === 'timed-question'" class="space-y-4">
      <div class="p-field">
        <label class="block text-sm font-medium text-surface-700 mb-1">Question</label>
        <InputText v-model="editItem.question" placeholder="Enter your open-ended question" class="w-full" />
      </div>

      <div class="p-field">
        <label class="block text-sm font-medium text-surface-700 mb-1">Predefined Answer</label>
        <Textarea
          v-model="editItem.timedAnswer"
          placeholder="Enter the answer that will be revealed after the timer"
          rows="3"
          class="w-full resize-none"
        />
      </div>

      <div class="p-field">
        <label class="block text-sm font-medium text-surface-700 mb-1">Reveal Time (seconds)</label>
        <InputNumber
          v-model="editItem.revealTimeSeconds"
          type="number"
          placeholder="30"
          :min="5"
          :max="300"
          class="w-full"
        />
        <small class="text-surface-500">Time before the answer is revealed (5-300 seconds)</small>
      </div>
    </div>

    <!-- Action Buttons -->
    <div class="flex gap-3 mt-6 pt-4 border-t border-surface-200">
      <Button
        label="Save Changes"
        icon="pi pi-check"
        @click="saveChanges"
        :disabled="!editItem.type || !editItem.title"
        class="flex-1"
      />
      <Button
        label="Cancel"
        icon="pi pi-times"
        severity="secondary"
        outlined
        @click="cancelEdit"
        class="flex-1"
      />
        </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue';
import { useMainStore } from '../../stores/main';
import type { Item } from '../../types/item';
import Select from 'primevue/select';
import InputText from 'primevue/inputtext';
import Textarea from 'primevue/textarea';
import FileUpload from 'primevue/fileupload';
import RadioButton from 'primevue/radiobutton';
import Button from 'primevue/button';
import Panel from 'primevue/panel';
import Avatar from 'primevue/avatar';
import Badge from 'primevue/badge';
import InputNumber from 'primevue/inputnumber';

interface Props {
  item: Item;
  bookId: string;
  chapterId: string;
  onSave?: (updatedItem: Item) => void;
  onCancel?: () => void;
}

const props = defineProps<Props>();
const store = useMainStore();

const editItem = ref<Item>({ ...props.item });
const itemTypes = ['question', 'test', 'text', 'image', 'link', 'map', 'diagram', 'timed-question'];

onMounted(() => {
  // Ensure options array exists for questions
  if (editItem.value.type === 'question' && !editItem.value.options) {
    editItem.value.options = [];
  }
});

const addOption = () => {
  if (!editItem.value.options) {
    editItem.value.options = [];
  }
  editItem.value.options.push({ text: '' });
};

const removeOption = (index: number) => {
  if (editItem.value.options && editItem.value.options.length > 1) {
    editItem.value.options.splice(index, 1);
    // Adjust correct answer if needed
    if (editItem.value.correctAnswer !== undefined && editItem.value.correctAnswer >= index) {
      if (editItem.value.correctAnswer === index) {
        editItem.value.correctAnswer = undefined;
      } else {
        editItem.value.correctAnswer--;
      }
    }
  }
};

const saveChanges = async () => {
  try {
    const updatedItem = await store.updateItem(
      props.bookId,
      props.chapterId,
      editItem.value.id,
      editItem.value
    );
    
    if (updatedItem && props.onSave) {
      props.onSave(updatedItem);
    }
  } catch (error) {
    console.error('Error updating item:', error);
  }
};

const cancelEdit = () => {
  if (props.onCancel) {
    props.onCancel();
  }
};
</script>

<style scoped>
/* Custom styles if needed */
</style>

// Script to update the existing admin user to super-admin role
// Run this once to upgrade your current admin user

import { initializeApp } from 'firebase/app';
import { getFirestore, doc, updateDoc, query, collection, where, getDocs } from 'firebase/firestore';

const firebaseConfig = {
  apiKey: "AIzaSyDzhwOOnCbpkuADs_T70SuocZ5ZmtNe4cY",
  authDomain: "textbook-platform.firebaseapp.com",
  projectId: "textbook-platform",
  storageBucket: "textbook-platform.firebasestorage.app",
  messagingSenderId: "376480732351",
  appId: "1:376480732351:web:95a0cd1e6837c0c3d2220a"
};

// Initialize Firebase
const app = initializeApp(firebaseConfig);
const db = getFirestore(app);

async function updateAdminRole() {
  try {
    console.log('Looking for existing admin users...');
    
    // Find all users with 'admin' role
    const adminQuery = query(
      collection(db, 'users'), 
      where('role', '==', 'admin')
    );
    
    const querySnapshot = await getDocs(adminQuery);
    
    if (querySnapshot.empty) {
      console.log('No admin users found to update.');
      process.exit(0);
    }
    
    console.log(`Found ${querySnapshot.size} admin user(s) to update...`);
    
    // Update each admin user to super-admin
    const updatePromises = [];
    querySnapshot.forEach((doc) => {
      const userData = doc.data();
      console.log(`Updating user: ${userData.email} (${userData.username})`);
      
      updatePromises.push(
        updateDoc(doc.ref, {
          role: 'super-admin'
        })
      );
    });
    
    await Promise.all(updatePromises);
    
    console.log('✅ Successfully updated admin users to super-admin role!');
    console.log('📝 The existing admin users now have super-admin privileges and can manage users.');
    console.log('🔧 You can now create new "admin" users who will have content management privileges only.');
    
    process.exit(0);
  } catch (error) {
    console.error('❌ Error updating admin roles:', error.message);
    process.exit(1);
  }
}

updateAdminRole();

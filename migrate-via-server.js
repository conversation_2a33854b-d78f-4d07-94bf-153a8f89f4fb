// Migration script that calls the server endpoint
// This script will authenticate and call the migration endpoint

import { initializeApp } from 'firebase/app';
import { getAuth, signInWithEmailAndPassword } from 'firebase/auth';

const firebaseConfig = {
  apiKey: "AIzaSyDzhwOOnCbpkuADs_T70SuocZ5ZmtNe4cY",
  authDomain: "textbook-platform.firebaseapp.com",
  projectId: "textbook-platform",
  storageBucket: "textbook-platform.firebasestorage.app",
  messagingSenderId: "376480732351",
  appId: "1:376480732351:web:95a0cd1e6837c0c3d2220a"
};

// Initialize Firebase
const app = initializeApp(firebaseConfig);
const auth = getAuth(app);

const SERVER_URL = 'https://my-textbook-app-376480732351.us-central1.run.app';

async function migrateData() {
  try {
    console.log('🔐 Signing in as admin...');
    
    // Sign in with admin credentials
    const userCredential = await signInWithEmailAndPassword(auth, '<EMAIL>', 'admin123456');
    const user = userCredential.user;
    
    console.log('✅ Signed in successfully');
    
    // Get ID token
    const idToken = await user.getIdToken();
    
    console.log('🔄 Calling migration endpoint...');
    
    // Call migration endpoint
    const response = await fetch(`${SERVER_URL}/api/migrate-to-firestore`, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${idToken}`,
        'Content-Type': 'application/json'
      }
    });
    
    if (!response.ok) {
      const errorData = await response.text();
      throw new Error(`Migration failed: ${response.status} - ${errorData}`);
    }
    
    const result = await response.json();
    
    console.log('✅ Migration completed successfully!');
    console.log('📊 Migration stats:');
    console.log(`   📚 Books: ${result.stats.books}`);
    console.log(`   📑 Chapters: ${result.stats.chapters}`);
    console.log(`   📄 Items: ${result.stats.items}`);
    
    console.log('\n🎉 Data migration complete!');
    console.log('📝 You can now:');
    console.log('   1. Update Firestore security rules in Firebase Console');
    console.log('   2. Test the application with real-time Firestore data');
    console.log('   3. QR codes should now work properly');
    
  } catch (error) {
    console.error('❌ Migration failed:', error.message);
    process.exit(1);
  }
}

// Run migration
migrateData().then(() => {
  console.log('🏁 Migration script completed');
  process.exit(0);
});

import { createApp } from 'vue'
import './styles/main.css'
import App from './App.vue'
import router from './router'
import { createPinia } from 'pinia'
import PrimeVue from 'primevue/config'
import Aura from '@primeuix/themes/aura'
import Tooltip from 'primevue/tooltip'
import ToastService from 'primevue/toastservice'
import ConfirmationService from 'primevue/confirmationservice'
import { useAuthStore } from './stores/auth'
import { useMainStore } from './stores/main'

const app = createApp(App)
const pinia = createPinia()
app.use(pinia)
app.use(router)
app.use(PrimeVue, {
    theme: {
        preset: Aura,
        options: {
            darkModeSelector: false || 'none'
        }
    }
})
app.use(ToastService)
app.use(ConfirmationService)
app.directive('tooltip', Tooltip)

// Initialize stores
const authStore = useAuthStore()
const mainStore = useMainStore()

// Initialize Firebase auth
authStore.initializeAuth()

// Initialize main store after a short delay to ensure auth is ready
setTimeout(() => {
  mainStore.initializeStore()
}, 500)

app.mount('#app')

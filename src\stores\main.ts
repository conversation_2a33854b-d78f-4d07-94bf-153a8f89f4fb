import { defineStore } from 'pinia';
import type { Book } from '../types/book';
import type { Item } from '../types/item';
import { useAuthStore } from './auth';

interface State {
  books: Book[];
  selectedBookId: string | null;
  loading: boolean;
  error: string | null;
  isRealTimeEnabled: boolean;
  unsubscribers: (() => void)[];
  // Loading states for individual actions
  actionLoading: {
    addingBook: boolean;
    addingChapter: boolean;
    addingItem: boolean;
    updatingItem: boolean;
    deletingItem: boolean;
    deletingChapter: boolean;
    deletingBook: boolean;
  };
}

export const useMainStore = defineStore('main', {
  state: (): State => ({
    books: [],
    selectedBookId: null,
    loading: false,
    error: null,
    isRealTimeEnabled: false,
    unsubscribers: [],
    actionLoading: {
      addingBook: false,
      addingChapter: false,
      addingItem: false,
      updatingItem: false,
      deletingItem: false,
      deletingChapter: false,
      deletingBook: false,
    },
  }),

  actions: {
    // Enable polling-based updates (simpler than Firestore listeners for now)
    enableRealTimeUpdates() {
      if (this.isRealTimeEnabled) {
        console.log('Real-time updates already enabled');
        return;
      }

      try {
        // Use polling instead of Firestore listeners to avoid permission issues
        const pollInterval = setInterval(async () => {
          try {
            await this.loadBooks();
          } catch (error) {
            console.error('❌ Polling update failed:', error);
          }
        }, 5000); // Poll every 5 seconds

        // Store the interval ID so we can clear it later
        this.unsubscribers.push(() => clearInterval(pollInterval));
        this.isRealTimeEnabled = true;
        console.log('✅ Real-time updates enabled (polling mode)');

      } catch (error) {
        console.error('❌ Failed to enable real-time updates:', error);
        this.error = `Failed to enable real-time updates: ${error instanceof Error ? error.message : 'Unknown error'}`;
      }
    },

    // Disable real-time listeners
    disableRealTimeUpdates() {
      this.unsubscribers.forEach(unsubscribe => unsubscribe());
      this.unsubscribers = [];
      this.isRealTimeEnabled = false;
      console.log('🔇 Real-time updates disabled');
    },

    // Initialize the store - try real-time first, fallback to API/static
    async initializeStore() {
      console.log('🚀 Initializing main store...');
      const authStore = useAuthStore();

      // Wait for auth to be initialized with a simple polling approach
      if (!authStore.initialized) {
        console.log('⏳ Waiting for auth initialization...');
        let attempts = 0;
        while (!authStore.initialized && attempts < 50) {
          await new Promise(resolve => setTimeout(resolve, 100));
          attempts++;
        }
        console.log(`✅ Auth initialized after ${attempts} attempts`);
      }

      // If user is authenticated, try to enable real-time updates
      if (authStore.user) {
        console.log('🔄 User authenticated, enabling real-time updates...');
        this.enableRealTimeUpdates();
      } else {
        console.log('👤 No user authenticated, loading books via API...');
        await this.loadBooks();
      }
      console.log(`📚 Store initialized with ${this.books.length} books`);
    },

    async loadBooks() {
      console.log('📖 Starting loadBooks...');
      // If real-time is enabled, don't load manually as data comes from listeners
      if (this.isRealTimeEnabled) {
        console.log('📡 Real-time updates active, skipping manual load');
        return;
      }

      console.log('🔄 Setting loading state...');
      this.loading = true;
      this.error = null;
      try {
        // Try API first, fallback to static file
        let response;
        try {
          const authStore = useAuthStore();
          const headers = await authStore.getAuthHeaders();
          response = await fetch('https://my-textbook-app-376480732351.us-central1.run.app/api/books', {
            headers: {
              ...headers,
            },
          });

          // If API call fails (e.g., 401 unauthorized), fall back to static file
          if (!response.ok) {
            console.log(`API call failed (${response.status}), falling back to static file`);
            response = await fetch('/books.json');
          }
        } catch (apiError) {
          console.log('API not available, falling back to static file');
          response = await fetch('/books.json');
        }

        if (!response.ok) {
          throw new Error(`Failed to load books: ${response.status} ${response.statusText}`);
        }
        const data = await response.json();
        console.log('Raw books data:', data);
        let booksArray = Array.isArray(data) ? data : Array.isArray(data.books) ? data.books : [];
        if (!Array.isArray(booksArray)) {
          throw new Error('Invalid books data: Expected an array or { books: [...] }');
        }
        this.books = booksArray;
      } catch (error) {
        this.error = String(error);
        this.books = [];
        console.error('Error loading books:', error);
      } finally {
        this.loading = false;
      }
    },

    async addBook(book: { title: string; description: string }) {
      this.actionLoading.addingBook = true;
      try {
        const authStore = useAuthStore();
        const headers = await authStore.getAuthHeaders();
        const response = await fetch('https://my-textbook-app-376480732351.us-central1.run.app/api/books', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            ...headers,
          },
          body: JSON.stringify(book),
        });

        if (!response.ok) {
          throw new Error(`Failed to add book: ${response.status}`);
        }

        const newBook = await response.json();
        this.books.push(newBook);
        return newBook;
      } catch (error) {
        console.error('Error adding book:', error);
        // Fallback to local state only
        const bookCount = this.books.length + 1;
        const fallbackBook = {
          id: `book${bookCount}`,
          title: book.title,
          description: book.description,
          chapters: []
        };
        this.books.push(fallbackBook);
        return fallbackBook;
      } finally {
        this.actionLoading.addingBook = false;
      }
    },

    selectBook(id: string | null) {
      this.selectedBookId = id;
    },

    async addChapter(bookId: string, chapter: { title: string }) {
      this.actionLoading.addingChapter = true;
      try {
        const authStore = useAuthStore();
        const headers = await authStore.getAuthHeaders();
        const response = await fetch(`https://my-textbook-app-376480732351.us-central1.run.app/api/books/${bookId}/chapters`, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            ...headers,
          },
          body: JSON.stringify(chapter),
        });

        if (!response.ok) {
          throw new Error(`Failed to add chapter: ${response.status}`);
        }

        const newChapter = await response.json();
        const book = this.books.find(b => b.id === bookId);
        if (book) {
          book.chapters.push(newChapter);
        }
        return newChapter;
      } catch (error) {
        console.error('Error adding chapter:', error);
        // Fallback to local state only
        const book = this.books.find(b => b.id === bookId);
        const chapterCount = book ? book.chapters.length + 1 : 1;
        const fallbackChapter = {
          id: `${bookId}-ch${chapterCount}`,
          title: chapter.title,
          items: []
        };
        if (book) {
          book.chapters.push(fallbackChapter);
        }
        return fallbackChapter;
      } finally {
        this.actionLoading.addingChapter = false;
      }
    },

    async addItem(bookId: string, chapterId: string, item: Omit<Item, 'id'>) {
      this.actionLoading.addingItem = true;
      try {
        const authStore = useAuthStore();
        const headers = await authStore.getAuthHeaders();
        const response = await fetch(`https://my-textbook-app-376480732351.us-central1.run.app/api/books/${bookId}/chapters/${chapterId}/items`, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            ...headers,
          },
          body: JSON.stringify(item),
        });

        if (!response.ok) {
          throw new Error(`Failed to add item: ${response.status}`);
        }

        const newItem = await response.json();
        const book = this.books.find(b => b.id === bookId);
        const chapter = book?.chapters.find(c => c.id === chapterId);
        if (chapter) {
          chapter.items.push(newItem);
        }
        return newItem;
      } catch (error) {
        console.error('Error adding item:', error);
        // Fallback to local state only
        const book = this.books.find(b => b.id === bookId);
        const chapter = book?.chapters.find(c => c.id === chapterId);
        const itemCount = chapter ? chapter.items.length + 1 : 1;
        const fallbackItem = {
          id: `${chapterId}-item${itemCount}`,
          ...item
        };
        if (chapter) {
          chapter.items.push(fallbackItem);
        }
        return fallbackItem;
      } finally {
        this.actionLoading.addingItem = false;
      }
    },

    async updateItem(bookId: string, chapterId: string, itemId: string, item: Partial<Item>) {
      this.actionLoading.updatingItem = true;
      try {
        const authStore = useAuthStore();
        const headers = await authStore.getAuthHeaders();
        const response = await fetch(`https://my-textbook-app-376480732351.us-central1.run.app/api/books/${bookId}/chapters/${chapterId}/items/${itemId}`, {
          method: 'PUT',
          headers: {
            'Content-Type': 'application/json',
            ...headers,
          },
          body: JSON.stringify(item),
        });

        if (!response.ok) {
          throw new Error(`Failed to update item: ${response.status}`);
        }

        const updatedItem = await response.json();
        const book = this.books.find(b => b.id === bookId);
        const chapter = book?.chapters.find(c => c.id === chapterId);
        if (chapter) {
          const itemIndex = chapter.items.findIndex(i => i.id === itemId);
          if (itemIndex !== -1) {
            chapter.items[itemIndex] = updatedItem;
          }
        }
        return updatedItem;
      } catch (error) {
        console.error('Error updating item:', error);
        // Fallback to local state only
        const book = this.books.find(b => b.id === bookId);
        const chapter = book?.chapters.find(c => c.id === chapterId);
        if (chapter) {
          const itemIndex = chapter.items.findIndex(i => i.id === itemId);
          if (itemIndex !== -1) {
            chapter.items[itemIndex] = { ...chapter.items[itemIndex], ...item };
          }
        }
        return chapter?.items.find(i => i.id === itemId);
      } finally {
        this.actionLoading.updatingItem = false;
      }
    },

    getBook(id: string | undefined): Book | undefined {
      return this.books.find(book => book.id === id);
    },

    async deleteBook(bookId: string) {
      this.actionLoading.deletingBook = true;
      try {
        const response = await fetch(`https://my-textbook-app-376480732351.us-central1.run.app/api/books/${bookId}`, {
          method: 'DELETE',
        });

        if (!response.ok) {
          throw new Error(`Failed to delete book: ${response.status}`);
        }

        // Remove from local state
        const bookIndex = this.books.findIndex(b => b.id === bookId);
        if (bookIndex !== -1) {
          this.books.splice(bookIndex, 1);
        }

        // Clear selection if deleted book was selected
        if (this.selectedBookId === bookId) {
          this.selectedBookId = null;
        }

        return true;
      } catch (error) {
        console.error('Error deleting book:', error);
        // Fallback to local state only
        const bookIndex = this.books.findIndex(b => b.id === bookId);
        if (bookIndex !== -1) {
          this.books.splice(bookIndex, 1);
        }
        if (this.selectedBookId === bookId) {
          this.selectedBookId = null;
        }
        return true;
      } finally {
        this.actionLoading.deletingBook = false;
      }
    },

    async deleteChapter(bookId: string, chapterId: string) {
      this.actionLoading.deletingChapter = true;
      try {
        const response = await fetch(`https://my-textbook-app-376480732351.us-central1.run.app/api/books/${bookId}/chapters/${chapterId}`, {
          method: 'DELETE',
        });

        if (!response.ok) {
          throw new Error(`Failed to delete chapter: ${response.status}`);
        }

        // Remove from local state
        const book = this.books.find(b => b.id === bookId);
        if (book) {
          const chapterIndex = book.chapters.findIndex(c => c.id === chapterId);
          if (chapterIndex !== -1) {
            book.chapters.splice(chapterIndex, 1);
          }
        }

        return true;
      } catch (error) {
        console.error('Error deleting chapter:', error);
        // Fallback to local state only
        const book = this.books.find(b => b.id === bookId);
        if (book) {
          const chapterIndex = book.chapters.findIndex(c => c.id === chapterId);
          if (chapterIndex !== -1) {
            book.chapters.splice(chapterIndex, 1);
          }
        }
        return true;
      } finally {
        this.actionLoading.deletingChapter = false;
      }
    },

    async deleteItem(bookId: string, chapterId: string, itemId: string) {
      this.actionLoading.deletingItem = true;
      try {
        const authStore = useAuthStore();
        const headers = await authStore.getAuthHeaders();
        const response = await fetch(`https://my-textbook-app-376480732351.us-central1.run.app/api/books/${bookId}/chapters/${chapterId}/items/${itemId}`, {
          method: 'DELETE',
          headers: {
            ...headers,
          },
        });

        if (!response.ok) {
          throw new Error(`Failed to delete item: ${response.status}`);
        }

        // Remove from local state
        const book = this.books.find(b => b.id === bookId);
        const chapter = book?.chapters.find(c => c.id === chapterId);
        if (chapter) {
          const itemIndex = chapter.items.findIndex(i => i.id === itemId);
          if (itemIndex !== -1) {
            chapter.items.splice(itemIndex, 1);
          }
        }

        return true;
      } catch (error) {
        console.error('Error deleting item:', error);
        // Fallback to local state only
        const book = this.books.find(b => b.id === bookId);
        const chapter = book?.chapters.find(c => c.id === chapterId);
        if (chapter) {
          const itemIndex = chapter.items.findIndex(i => i.id === itemId);
          if (itemIndex !== -1) {
            chapter.items.splice(itemIndex, 1);
          }
        }
        return true;
      } finally {
        this.actionLoading.deletingItem = false;
      }
    },

    // Handle authentication state changes
    onAuthStateChange(isAuthenticated: boolean) {
      if (isAuthenticated) {
        console.log('🔐 User authenticated, enabling real-time updates...');
        this.enableRealTimeUpdates();
      } else {
        console.log('🔓 User signed out, disabling real-time updates...');
        this.disableRealTimeUpdates();
        // Clear books data when user signs out for security
        this.books = [];
        this.selectedBookId = null;
      }
    },

    // Cleanup method to call when store is destroyed
    cleanup() {
      this.disableRealTimeUpdates();
      console.log('🧹 Store cleanup completed');
    },
  },
});
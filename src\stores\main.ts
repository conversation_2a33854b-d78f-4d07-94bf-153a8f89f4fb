import { defineStore } from 'pinia';
import type { Book } from '../types/book';
import type { Item } from '../types/item';
import { useAuthStore } from './auth';
import { collection, onSnapshot, getDocs } from 'firebase/firestore';
import { db } from '../config/firebase';

interface State {
  books: Book[];
  selectedBookId: string | null;
  loading: boolean;
  error: string | null;
  isRealTimeEnabled: boolean;
  unsubscribers: (() => void)[];
  // Loading states for individual actions
  actionLoading: {
    addingBook: boolean;
    addingChapter: boolean;
    addingItem: boolean;
    updatingItem: boolean;
    deletingItem: boolean;
    deletingChapter: boolean;
    deletingBook: boolean;
  };
}

export const useMainStore = defineStore('main', {
  state: (): State => ({
    books: [],
    selectedBookId: null,
    loading: false,
    error: null,
    isRealTimeEnabled: false,
    unsubscribers: [],
    actionLoading: {
      addingBook: false,
      addingChapter: false,
      addingItem: false,
      updatingItem: false,
      deletingItem: false,
      deletingChapter: false,
      deletingBook: false,
    },
  }),

  actions: {
    // Enable polling-based updates (simpler than Firestore listeners for now)
    // Disable real-time listeners
    disableRealTimeUpdates() {
      this.unsubscribers.forEach(unsubscribe => unsubscribe());
      this.unsubscribers = [];
      this.isRealTimeEnabled = false;
      console.log('🔇 Real-time updates disabled');
    },

    // Initialize the store - load books directly from Firestore
    async initializeStore() {
      console.log('🚀 Initializing main store...');
      const authStore = useAuthStore();

      // Wait for auth to be initialized with a simple polling approach
      if (!authStore.initialized) {
        console.log('⏳ Waiting for auth initialization...');
        let attempts = 0;
        while (!authStore.initialized && attempts < 50) {
          await new Promise(resolve => setTimeout(resolve, 100));
          attempts++;
        }
        console.log(`✅ Auth initialized after ${attempts} attempts`);
      }

      // Only load books if user is authenticated
      if (authStore.user) {
        console.log('📚 User authenticated, loading books from Firestore...');
        await this.loadBooksFromFirestore();
        console.log(`📚 Store initialized with ${this.books.length} books`);
      } else {
        console.log('🔒 User not authenticated, skipping book loading');
        // Load books from static file as fallback for unauthenticated users
        await this.loadBooksFromStatic();
      }
    },

    // Called when user authentication state changes
    async onAuthStateChange(isAuthenticated: boolean) {
      console.log(`🔐 Auth state changed: ${isAuthenticated ? 'authenticated' : 'not authenticated'}`);

      if (isAuthenticated) {
        // User signed in - load books from Firestore
        console.log('📚 User signed in, loading books from Firestore...');
        await this.loadBooksFromFirestore();
      } else {
        // User signed out - clear books and load static fallback
        console.log('🔒 User signed out, clearing books and loading static fallback...');
        this.cleanup();
        await this.loadBooksFromStatic();
      }
    },

    // Load books directly from Firestore with real-time updates
    async loadBooksFromFirestore() {
      console.log('📚 Loading books from Firestore...');
      this.loading = true;
      this.error = null;

      try {
        // Set up real-time listener for books collection
        const booksCollection = collection(db, 'books');

        // Use onSnapshot for real-time updates
        const unsubscribe = onSnapshot(booksCollection, async () => {
          console.log('📡 Books collection changed, reloading all data...');
          await this.loadCompleteBookData();
        }, (error) => {
          console.error('❌ Firestore listener error:', error);
          // Fall back to static file if Firestore fails
          this.loadBooksFromStatic();
        });

        // Store the unsubscriber
        this.unsubscribers.push(unsubscribe);

        // Load initial data
        await this.loadCompleteBookData();

      } catch (error) {
        console.error('❌ Failed to set up Firestore listener:', error);
        // Fall back to static file
        await this.loadBooksFromStatic();
      }
    },

    // Load complete book data with chapters and items (similar to server logic)
    async loadCompleteBookData() {
      console.log('🔄 Starting loadCompleteBookData...');
      this.loading = true;
      this.error = null;

      try {
        const books: Book[] = [];
        const booksCollection = collection(db, 'books');
        console.log('📖 Getting books collection...');
        const booksSnapshot = await getDocs(booksCollection);
        console.log(`📖 Found ${booksSnapshot.docs.length} book documents`);

        for (const bookDoc of booksSnapshot.docs) {
          const bookData = bookDoc.data();
          console.log(`📖 Processing book: ${bookDoc.id} - ${bookData.title}`);
          const book: Book = {
            id: bookDoc.id,
            title: bookData.title || '',
            description: bookData.description || '',
            chapters: []
          };

          try {
            // Load chapters for this book
            const chaptersCollection = collection(db, 'books', bookDoc.id, 'chapters');
            const chaptersSnapshot = await getDocs(chaptersCollection);
            console.log(`📚 Found ${chaptersSnapshot.docs.length} chapters for book ${bookDoc.id}`);

            for (const chapterDoc of chaptersSnapshot.docs) {
              const chapterData = chapterDoc.data();
              console.log(`📚 Processing chapter: ${chapterDoc.id} - ${chapterData.title}`);
              const chapter = {
                id: chapterDoc.id,
                title: chapterData.title || '',
                items: [] as Item[]
              };

              try {
                // Load items for this chapter
                const itemsCollection = collection(db, 'books', bookDoc.id, 'chapters', chapterDoc.id, 'items');
                const itemsSnapshot = await getDocs(itemsCollection);
                console.log(`📄 Found ${itemsSnapshot.docs.length} items for chapter ${chapterDoc.id}`);

                itemsSnapshot.docs.forEach((itemDoc) => {
                  const itemData = itemDoc.data();
                  console.log(`📄 Processing item: ${itemDoc.id} - ${itemData.title}`);
                  chapter.items.push({
                    id: itemDoc.id,
                    ...itemData
                  } as Item);
                });
              } catch (itemsError) {
                console.warn(`⚠️ Failed to load items for chapter ${chapterDoc.id}:`, itemsError);
              }

              book.chapters.push(chapter);
            }
          } catch (chaptersError) {
            console.warn(`⚠️ Failed to load chapters for book ${bookDoc.id}:`, chaptersError);
          }

          books.push(book);
        }

        this.books = books;
        this.loading = false;
        console.log(`✅ Loaded ${books.length} books with chapters and items from Firestore:`, books);
      } catch (error) {
        console.error('❌ Failed to load complete book data:', error);
        this.error = String(error);
        this.loading = false;
        // Fall back to static file if Firestore fails
        console.log('🔄 Falling back to static file...');
        await this.loadBooksFromStatic();
      }
    },

    // Fallback method to load from static file
    async loadBooksFromStatic() {
      console.log('📄 Loading books from static file...');
      try {
        const response = await fetch('/books.json');
        if (!response.ok) {
          throw new Error(`Failed to load static books: ${response.status}`);
        }
        const data = await response.json();
        let booksArray = Array.isArray(data) ? data : Array.isArray(data.books) ? data.books : [];
        this.books = booksArray;
        console.log(`✅ Loaded ${booksArray.length} books from static file`);
      } catch (error) {
        console.error('❌ Failed to load static books:', error);
        this.error = String(error);
        this.books = [];
      } finally {
        this.loading = false;
      }
    },

    async addBook(book: { title: string; description: string }) {
      this.actionLoading.addingBook = true;
      try {
        const authStore = useAuthStore();
        const headers = await authStore.getAuthHeaders();
        const response = await fetch('https://my-textbook-app-376480732351.us-central1.run.app/api/books', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            ...headers,
          },
          body: JSON.stringify(book),
        });

        if (!response.ok) {
          throw new Error(`Failed to add book: ${response.status}`);
        }

        const newBook = await response.json();
        this.books.push(newBook);
        return newBook;
      } catch (error) {
        console.error('Error adding book:', error);
        // Fallback to local state only
        const bookCount = this.books.length + 1;
        const fallbackBook = {
          id: `book${bookCount}`,
          title: book.title,
          description: book.description,
          chapters: []
        };
        this.books.push(fallbackBook);
        return fallbackBook;
      } finally {
        this.actionLoading.addingBook = false;
      }
    },

    selectBook(id: string | null) {
      this.selectedBookId = id;
    },

    async addChapter(bookId: string, chapter: { title: string }) {
      this.actionLoading.addingChapter = true;
      try {
        const authStore = useAuthStore();
        const headers = await authStore.getAuthHeaders();
        const response = await fetch(`https://my-textbook-app-376480732351.us-central1.run.app/api/books/${bookId}/chapters`, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            ...headers,
          },
          body: JSON.stringify(chapter),
        });

        if (!response.ok) {
          throw new Error(`Failed to add chapter: ${response.status}`);
        }

        const newChapter = await response.json();
        const book = this.books.find(b => b.id === bookId);
        if (book) {
          book.chapters.push(newChapter);
        }
        return newChapter;
      } catch (error) {
        console.error('Error adding chapter:', error);
        // Fallback to local state only
        const book = this.books.find(b => b.id === bookId);
        const chapterCount = book ? book.chapters.length + 1 : 1;
        const fallbackChapter = {
          id: `${bookId}-ch${chapterCount}`,
          title: chapter.title,
          items: []
        };
        if (book) {
          book.chapters.push(fallbackChapter);
        }
        return fallbackChapter;
      } finally {
        this.actionLoading.addingChapter = false;
      }
    },

    async addItem(bookId: string, chapterId: string, item: Omit<Item, 'id'>) {
      this.actionLoading.addingItem = true;
      try {
        const authStore = useAuthStore();
        const headers = await authStore.getAuthHeaders();
        const response = await fetch(`https://my-textbook-app-376480732351.us-central1.run.app/api/books/${bookId}/chapters/${chapterId}/items`, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            ...headers,
          },
          body: JSON.stringify(item),
        });

        if (!response.ok) {
          throw new Error(`Failed to add item: ${response.status}`);
        }

        const newItem = await response.json();
        const book = this.books.find(b => b.id === bookId);
        const chapter = book?.chapters.find(c => c.id === chapterId);
        if (chapter) {
          chapter.items.push(newItem);
        }
        return newItem;
      } catch (error) {
        console.error('Error adding item:', error);
        // Fallback to local state only
        const book = this.books.find(b => b.id === bookId);
        const chapter = book?.chapters.find(c => c.id === chapterId);
        const itemCount = chapter ? chapter.items.length + 1 : 1;
        const fallbackItem = {
          id: `${chapterId}-item${itemCount}`,
          ...item
        };
        if (chapter) {
          chapter.items.push(fallbackItem);
        }
        return fallbackItem;
      } finally {
        this.actionLoading.addingItem = false;
      }
    },

    async updateItem(bookId: string, chapterId: string, itemId: string, item: Partial<Item>) {
      this.actionLoading.updatingItem = true;
      try {
        const authStore = useAuthStore();
        const headers = await authStore.getAuthHeaders();
        const response = await fetch(`https://my-textbook-app-376480732351.us-central1.run.app/api/books/${bookId}/chapters/${chapterId}/items/${itemId}`, {
          method: 'PUT',
          headers: {
            'Content-Type': 'application/json',
            ...headers,
          },
          body: JSON.stringify(item),
        });

        if (!response.ok) {
          throw new Error(`Failed to update item: ${response.status}`);
        }

        const updatedItem = await response.json();
        const book = this.books.find(b => b.id === bookId);
        const chapter = book?.chapters.find(c => c.id === chapterId);
        if (chapter) {
          const itemIndex = chapter.items.findIndex(i => i.id === itemId);
          if (itemIndex !== -1) {
            chapter.items[itemIndex] = updatedItem;
          }
        }
        return updatedItem;
      } catch (error) {
        console.error('Error updating item:', error);
        // Fallback to local state only
        const book = this.books.find(b => b.id === bookId);
        const chapter = book?.chapters.find(c => c.id === chapterId);
        if (chapter) {
          const itemIndex = chapter.items.findIndex(i => i.id === itemId);
          if (itemIndex !== -1) {
            chapter.items[itemIndex] = { ...chapter.items[itemIndex], ...item };
          }
        }
        return chapter?.items.find(i => i.id === itemId);
      } finally {
        this.actionLoading.updatingItem = false;
      }
    },

    getBook(id: string | undefined): Book | undefined {
      return this.books.find(book => book.id === id);
    },

    async deleteBook(bookId: string) {
      this.actionLoading.deletingBook = true;
      try {
        const response = await fetch(`https://my-textbook-app-376480732351.us-central1.run.app/api/books/${bookId}`, {
          method: 'DELETE',
        });

        if (!response.ok) {
          throw new Error(`Failed to delete book: ${response.status}`);
        }

        // Remove from local state
        const bookIndex = this.books.findIndex(b => b.id === bookId);
        if (bookIndex !== -1) {
          this.books.splice(bookIndex, 1);
        }

        // Clear selection if deleted book was selected
        if (this.selectedBookId === bookId) {
          this.selectedBookId = null;
        }

        return true;
      } catch (error) {
        console.error('Error deleting book:', error);
        // Fallback to local state only
        const bookIndex = this.books.findIndex(b => b.id === bookId);
        if (bookIndex !== -1) {
          this.books.splice(bookIndex, 1);
        }
        if (this.selectedBookId === bookId) {
          this.selectedBookId = null;
        }
        return true;
      } finally {
        this.actionLoading.deletingBook = false;
      }
    },

    async deleteChapter(bookId: string, chapterId: string) {
      this.actionLoading.deletingChapter = true;
      try {
        const response = await fetch(`https://my-textbook-app-376480732351.us-central1.run.app/api/books/${bookId}/chapters/${chapterId}`, {
          method: 'DELETE',
        });

        if (!response.ok) {
          throw new Error(`Failed to delete chapter: ${response.status}`);
        }

        // Remove from local state
        const book = this.books.find(b => b.id === bookId);
        if (book) {
          const chapterIndex = book.chapters.findIndex(c => c.id === chapterId);
          if (chapterIndex !== -1) {
            book.chapters.splice(chapterIndex, 1);
          }
        }

        return true;
      } catch (error) {
        console.error('Error deleting chapter:', error);
        // Fallback to local state only
        const book = this.books.find(b => b.id === bookId);
        if (book) {
          const chapterIndex = book.chapters.findIndex(c => c.id === chapterId);
          if (chapterIndex !== -1) {
            book.chapters.splice(chapterIndex, 1);
          }
        }
        return true;
      } finally {
        this.actionLoading.deletingChapter = false;
      }
    },

    async deleteItem(bookId: string, chapterId: string, itemId: string) {
      this.actionLoading.deletingItem = true;
      try {
        const authStore = useAuthStore();
        const headers = await authStore.getAuthHeaders();
        const response = await fetch(`https://my-textbook-app-376480732351.us-central1.run.app/api/books/${bookId}/chapters/${chapterId}/items/${itemId}`, {
          method: 'DELETE',
          headers: {
            ...headers,
          },
        });

        if (!response.ok) {
          throw new Error(`Failed to delete item: ${response.status}`);
        }

        // Remove from local state
        const book = this.books.find(b => b.id === bookId);
        const chapter = book?.chapters.find(c => c.id === chapterId);
        if (chapter) {
          const itemIndex = chapter.items.findIndex(i => i.id === itemId);
          if (itemIndex !== -1) {
            chapter.items.splice(itemIndex, 1);
          }
        }

        return true;
      } catch (error) {
        console.error('Error deleting item:', error);
        // Fallback to local state only
        const book = this.books.find(b => b.id === bookId);
        const chapter = book?.chapters.find(c => c.id === chapterId);
        if (chapter) {
          const itemIndex = chapter.items.findIndex(i => i.id === itemId);
          if (itemIndex !== -1) {
            chapter.items.splice(itemIndex, 1);
          }
        }
        return true;
      } finally {
        this.actionLoading.deletingItem = false;
      }
    },



    // Cleanup method to call when store is destroyed
    cleanup() {
      this.disableRealTimeUpdates();
      console.log('🧹 Store cleanup completed');
    },
  },
});
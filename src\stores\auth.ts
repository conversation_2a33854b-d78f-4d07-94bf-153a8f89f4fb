import { defineStore } from 'pinia';
import type { User, LoginCredentials, Permission, UserRole } from '../types/user';
import { getRolePermissions } from '../types/user';
import { firebaseAuthService, type CreateUserData } from '../services/firebase-auth';
import type { User as FirebaseUser } from 'firebase/auth';

interface AuthState {
  user: User | null;
  firebaseUser: FirebaseUser | null;
  permissions: Permission | null;
  loading: boolean;
  error: string | null;
  initialized: boolean;
}

export const useAuthStore = defineStore('auth', {
  state: (): AuthState => ({
    user: null,
    firebaseUser: null,
    permissions: null,
    loading: false,
    error: null,
    initialized: false,
  }),

  getters: {
    isAuthenticated: (state) => !!state.firebaseUser && !!state.user,
    isAdmin: (state) => state.user?.role === 'admin',
    isEditor: (state) => state.user?.role === 'editor' || state.user?.role === 'admin',
    isViewer: (state) => !!state.user,

    canView: (state) => state.permissions?.canView ?? false,
    canEdit: (state) => state.permissions?.canEdit ?? false,
    canDelete: (state) => state.permissions?.canDelete ?? false,
    canManageUsers: (state) => state.permissions?.canManageUsers ?? false,
    canCreateBooks: (state) => state.permissions?.canCreateBooks ?? false,
    canDeleteBooks: (state) => state.permissions?.canDeleteBooks ?? false,
    canCreateChapters: (state) => state.permissions?.canCreateChapters ?? false,
    canDeleteChapters: (state) => state.permissions?.canDeleteChapters ?? false,
  },

  actions: {
    // Initialize Firebase auth listener
    initializeAuth() {
      if (this.initialized) return;

      this.loading = true;
      firebaseAuthService.onAuthStateChanged(async (firebaseUser) => {
        const wasAuthenticated = !!this.user;

        if (firebaseUser) {
          // User is signed in
          this.firebaseUser = firebaseUser;

          // Get user profile from Firestore
          const userProfile = await firebaseAuthService.getUserProfile(firebaseUser.uid);
          if (userProfile) {
            this.user = {
              id: userProfile.uid,
              username: userProfile.username,
              email: userProfile.email,
              role: userProfile.role,
              createdAt: userProfile.createdAt,
              lastLogin: userProfile.lastLogin
            };
            this.permissions = getRolePermissions(userProfile.role);
          }
        } else {
          // User is signed out
          this.firebaseUser = null;
          this.user = null;
          this.permissions = null;
        }

        this.loading = false;
        this.initialized = true;

        // Notify main store of auth state change
        const isNowAuthenticated = !!this.user;
        if (wasAuthenticated !== isNowAuthenticated) {
          // Import here to avoid circular dependency
          const { useMainStore } = await import('./main');
          const mainStore = useMainStore();
          mainStore.onAuthStateChange(isNowAuthenticated);
        }
      });
    },

    async login(credentials: LoginCredentials) {
      this.loading = true;
      this.error = null;

      try {
        // Use email as username for Firebase auth
        const user = await firebaseAuthService.signIn(credentials.username, credentials.password);

        this.user = user;
        this.permissions = getRolePermissions(user.role);

        return true;
      } catch (error) {
        this.error = error instanceof Error ? error.message : 'Login failed';
        return false;
      } finally {
        this.loading = false;
      }
    },

    async logout() {
      this.loading = true;
      this.error = null;

      try {
        await firebaseAuthService.signOut();
        // State will be cleared by the auth state listener
      } catch (error) {
        this.error = error instanceof Error ? error.message : 'Logout failed';
        console.error('Logout error:', error);
      } finally {
        this.loading = false;
      }
    },

    // Create a new user account (admin only)
    async createUser(userData: CreateUserData) {
      this.loading = true;
      this.error = null;

      try {
        const user = await firebaseAuthService.createUser(userData);
        return user;
      } catch (error) {
        this.error = error instanceof Error ? error.message : 'Failed to create user';
        throw error;
      } finally {
        this.loading = false;
      }
    },

    // Get all users (admin only)
    async getAllUsers() {
      this.loading = true;
      this.error = null;

      try {
        const users = await firebaseAuthService.getAllUsers();
        return users;
      } catch (error) {
        this.error = error instanceof Error ? error.message : 'Failed to get users';
        throw error;
      } finally {
        this.loading = false;
      }
    },

    // Update user role (admin only)
    async updateUserRole(uid: string, newRole: UserRole) {
      this.loading = true;
      this.error = null;

      try {
        await firebaseAuthService.updateUserRole(uid, newRole);
      } catch (error) {
        this.error = error instanceof Error ? error.message : 'Failed to update user role';
        throw error;
      } finally {
        this.loading = false;
      }
    },

    // Delete user (admin only)
    async deleteUser(uid: string) {
      this.loading = true;
      this.error = null;

      try {
        await firebaseAuthService.deleteUser(uid);
      } catch (error) {
        this.error = error instanceof Error ? error.message : 'Failed to delete user';
        throw error;
      } finally {
        this.loading = false;
      }
    },

    // Helper method to get Firebase ID token for API calls
    async getAuthHeaders(): Promise<Record<string, string>> {
      if (this.firebaseUser) {
        try {
          const token = await this.firebaseUser.getIdToken();
          return { 'Authorization': `Bearer ${token}` };
        } catch (error) {
          console.error('Error getting ID token:', error);
        }
      }
      return {};
    },

    clearError() {
      this.error = null;
    }
  },
});

# Lightweight server-only Dockerfile
FROM node:18-alpine

# Install only the system dependencies needed for the server
RUN apk add --no-cache \
    python3 \
    py3-pip \
    make \
    g++ \
    cairo-dev \
    jpeg-dev \
    pango-dev \
    musl-dev \
    giflib-dev \
    pixman-dev \
    pangomm-dev \
    libjpeg-turbo-dev \
    freetype-dev

# Set working directory
WORKDIR /app

# Copy server-specific package.json
COPY server-package.json package.json

# Install only server dependencies
RUN npm install --only=production

# Copy only server file
COPY server.cjs ./

# Expose port 8080
EXPOSE 8080

# Set environment variables
ENV NODE_ENV=production

# Start the server
CMD ["node", "server.cjs"]

# Lightweight server-only Dockerfile
FROM node:18-alpine

# Install only the system dependencies needed for the server
RUN apk add --no-cache \
    python3 \
    py3-pip \
    make \
    g++ \
    cairo-dev \
    jpeg-dev \
    pango-dev \
    musl-dev \
    giflib-dev \
    pixman-dev \
    pangomm-dev \
    libjpeg-turbo-dev \
    freetype-dev

# Set working directory
WORKDIR /app

# Copy only server dependencies
COPY package*.json ./

# Install only production dependencies needed for server
RUN npm ci --omit=dev

# Copy only server file and public assets
COPY server.cjs ./
COPY public ./public

# Expose port 8080
EXPOSE 8080

# Set environment variables
ENV NODE_ENV=production

# Start the server
CMD ["node", "server.cjs"]

# Node.js dependencies
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# Development files
.env.local
.env.development.local
.env.test.local
.env.production.local

# IDE files
.vscode/
.idea/
*.swp
*.swo

# OS files
.DS_Store
Thumbs.db

# Git
.git/
.gitignore

# Frontend source files (not needed for server deployment)
src/
public/
index.html
vite.config.ts
tsconfig.json
tsconfig.app.json
tsconfig.node.json
tailwind.config.js

# Build artifacts (will be rebuilt in container)
dist/

# Documentation
README.md
*.md

# Migration scripts (not needed in production)
migrate-to-firestore.cjs
update-admin-role.js
migrate-via-server.js

# Local development files
books.json

# Firebase config files (not needed on server)
firestore-security-rules.txt

# Development scripts
fast-deploy.ps1

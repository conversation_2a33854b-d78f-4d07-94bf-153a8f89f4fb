 # Node.js dependencies
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# Development files
.env.local
.env.development.local
.env.test.local
.env.production.local

# IDE files
.vscode/
.idea/
*.swp
*.swo

# OS files
.DS_Store
Thumbs.db

# Git
.git/
.gitignore

# Build artifacts (will be rebuilt in container)
dist/

# Documentation
README.md
*.md

# Migration scripts (not needed in production)
migrate-to-firestore.cjs
update-admin-role.js

# Local development files
books.json

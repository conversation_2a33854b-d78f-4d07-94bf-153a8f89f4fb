<template>
  <div class="user-management">
    <div class="flex justify-between items-center mb-6">
      <h2 class="text-2xl font-bold">User Management</h2>
      <Button 
        label="Create User" 
        icon="pi pi-plus" 
        @click="showCreateDialog = true"
        :disabled="!authStore.canManageUsers"
      />
    </div>

    <!-- Users Table -->
    <DataTable 
      :value="users" 
      :loading="loading"
      paginator 
      :rows="10"
      class="border border-surface-300 rounded-xl"
    >
      <Column field="username" header="Username" sortable />
      <Column field="email" header="Email" sortable />
      <Column field="role" header="Role" sortable>
        <template #body="{ data }">
          <Tag
            :value="getRoleLabel(data.role)"
            :severity="getRoleSeverity(data.role)"
          />
        </template>
      </Column>
      <Column field="createdAt" header="Created" sortable>
        <template #body="{ data }">
          {{ formatDate(data.createdAt) }}
        </template>
      </Column>
      <Column field="lastLogin" header="Last Login" sortable>
        <template #body="{ data }">
          {{ data.lastLogin ? formatDate(data.lastLogin) : 'Never' }}
        </template>
      </Column>
      <Column header="Actions">
        <template #body="{ data }">
          <div class="flex gap-2">
            <Button 
              icon="pi pi-pencil" 
              size="small" 
              text
              @click="editUser(data)"
              :disabled="!authStore.canManageUsers"
            />
            <Button 
              icon="pi pi-trash" 
              size="small" 
              text
              severity="danger"
              @click="confirmDeleteUser(data)"
              :disabled="!authStore.canManageUsers || data.uid === authStore.user?.id"
            />
          </div>
        </template>
      </Column>
    </DataTable>

    <!-- Create User Dialog -->
    <Dialog 
      v-model:visible="showCreateDialog" 
      header="Create New User" 
      modal 
      :style="{ width: '450px' }"
    >
      <form @submit.prevent="createUser" class="space-y-4">
        <div>
          <label class="block text-sm font-medium mb-2">Username</label>
          <InputText 
            v-model="newUser.username" 
            placeholder="Enter username"
            class="w-full"
            required
          />
        </div>
        
        <div>
          <label class="block text-sm font-medium mb-2">Email</label>
          <InputText 
            v-model="newUser.email" 
            type="email"
            placeholder="Enter email"
            class="w-full"
            required
          />
        </div>
        
        <div>
          <label class="block text-sm font-medium mb-2">Password</label>
          <Password 
            v-model="newUser.password" 
            placeholder="Enter password"
            class="w-full"
            :feedback="false"
            toggleMask
            required
          />
        </div>
        
        <div>
          <label class="block text-sm font-medium mb-2">Role</label>
          <Select 
            v-model="newUser.role" 
            :options="roleOptions"
            optionLabel="label"
            optionValue="value"
            placeholder="Select role"
            class="w-full"
            required
          />
        </div>

        <div class="flex justify-end gap-2 pt-4">
          <Button 
            label="Cancel" 
            text 
            @click="showCreateDialog = false"
          />
          <Button 
            label="Create" 
            type="submit"
            :loading="authStore.loading"
          />
        </div>
      </form>
    </Dialog>

    <!-- Edit User Dialog -->
    <Dialog 
      v-model:visible="showEditDialog" 
      header="Edit User" 
      modal 
      :style="{ width: '450px' }"
    >
      <form @submit.prevent="updateUser" class="space-y-4" v-if="editingUser">
        <div>
          <label class="block text-sm font-medium mb-2">Username</label>
          <InputText 
            :value="editingUser.username" 
            disabled
            class="w-full"
          />
        </div>
        
        <div>
          <label class="block text-sm font-medium mb-2">Email</label>
          <InputText 
            :value="editingUser.email" 
            disabled
            class="w-full"
          />
        </div>
        
        <div>
          <label class="block text-sm font-medium mb-2">Role</label>
          <Select 
            v-model="editingUser.role" 
            :options="roleOptions"
            optionLabel="label"
            optionValue="value"
            class="w-full"
            required
          />
        </div>

        <div class="flex justify-end gap-2 pt-4">
          <Button 
            label="Cancel" 
            text 
            @click="showEditDialog = false"
          />
          <Button 
            label="Update" 
            type="submit"
            :loading="authStore.loading"
          />
        </div>
      </form>
    </Dialog>

    <!-- Delete Confirmation Dialog -->
    <ConfirmDialog />
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue';
import { useAuthStore } from '../stores/auth';
import { useConfirm } from 'primevue/useconfirm';
import { useToast } from 'primevue/usetoast';
import type { UserRole } from '../types/user';
import type { UserProfile } from '../services/firebase-auth';

import Button from 'primevue/button';
import DataTable from 'primevue/datatable';
import Column from 'primevue/column';
import Dialog from 'primevue/dialog';
import InputText from 'primevue/inputtext';
import Password from 'primevue/password';
import Select from 'primevue/select';
import Tag from 'primevue/tag';
import ConfirmDialog from 'primevue/confirmdialog';

const authStore = useAuthStore();
const confirm = useConfirm();
const toast = useToast();

const users = ref<UserProfile[]>([]);
const loading = ref(false);
const showCreateDialog = ref(false);
const showEditDialog = ref(false);
const editingUser = ref<UserProfile | null>(null);

const newUser = ref({
  username: '',
  email: '',
  password: '',
  role: 'viewer' as UserRole
});

const roleOptions = [
  { label: 'Viewer', value: 'viewer' },
  { label: 'Editor', value: 'editor' },
  { label: 'Content Admin', value: 'admin' },
  { label: 'Super Admin', value: 'super-admin' }
];

const getRoleLabel = (role: UserRole) => {
  switch (role) {
    case 'super-admin': return 'Super Admin';
    case 'admin': return 'Content Admin';
    case 'editor': return 'Editor';
    case 'viewer': return 'Viewer';
    default: return role;
  }
};

const getRoleSeverity = (role: UserRole) => {
  switch (role) {
    case 'super-admin': return 'danger';
    case 'admin': return 'warn';
    case 'editor': return 'info';
    case 'viewer': return 'secondary';
    default: return 'secondary';
  }
};

const formatDate = (timestamp: number) => {
  return new Date(timestamp).toLocaleDateString();
};

const loadUsers = async () => {
  if (!authStore.canManageUsers) return;
  
  loading.value = true;
  try {
    users.value = await authStore.getAllUsers();
  } catch (error) {
    toast.add({
      severity: 'error',
      summary: 'Error',
      detail: 'Failed to load users',
      life: 3000
    });
  } finally {
    loading.value = false;
  }
};

const createUser = async () => {
  try {
    await authStore.createUser(newUser.value);
    
    toast.add({
      severity: 'success',
      summary: 'Success',
      detail: 'User created successfully',
      life: 3000
    });
    
    showCreateDialog.value = false;
    newUser.value = { username: '', email: '', password: '', role: 'viewer' };
    await loadUsers();
  } catch (error) {
    toast.add({
      severity: 'error',
      summary: 'Error',
      detail: authStore.error || 'Failed to create user',
      life: 3000
    });
  }
};

const editUser = (user: UserProfile) => {
  editingUser.value = { ...user };
  showEditDialog.value = true;
};

const updateUser = async () => {
  if (!editingUser.value) return;
  
  try {
    await authStore.updateUserRole(editingUser.value.uid, editingUser.value.role);
    
    toast.add({
      severity: 'success',
      summary: 'Success',
      detail: 'User updated successfully',
      life: 3000
    });
    
    showEditDialog.value = false;
    editingUser.value = null;
    await loadUsers();
  } catch (error) {
    toast.add({
      severity: 'error',
      summary: 'Error',
      detail: authStore.error || 'Failed to update user',
      life: 3000
    });
  }
};

const confirmDeleteUser = (user: UserProfile) => {
  confirm.require({
    message: `Are you sure you want to delete user "${user.username}"?`,
    header: 'Confirm Deletion',
    icon: 'pi pi-exclamation-triangle',
    rejectProps: {
      label: 'Cancel',
      severity: 'secondary',
      outlined: true
    },
    acceptProps: {
      label: 'Delete',
      severity: 'danger'
    },
    accept: () => deleteUser(user.uid)
  });
};

const deleteUser = async (uid: string) => {
  try {
    await authStore.deleteUser(uid);
    
    toast.add({
      severity: 'success',
      summary: 'Success',
      detail: 'User deleted successfully',
      life: 3000
    });
    
    await loadUsers();
  } catch (error) {
    toast.add({
      severity: 'error',
      summary: 'Error',
      detail: authStore.error || 'Failed to delete user',
      life: 3000
    });
  }
};

onMounted(() => {
  if (authStore.canManageUsers) {
    loadUsers();
  }
});
</script>

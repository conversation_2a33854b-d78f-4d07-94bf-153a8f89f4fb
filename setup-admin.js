// Setup script to create initial admin user
// Run this once to create your first admin user

import { initializeApp } from 'firebase/app';
import { getAuth, createUserWithEmailAndPassword, updateProfile } from 'firebase/auth';
import { getFirestore, doc, setDoc } from 'firebase/firestore';

const firebaseConfig = {
  apiKey: "AIzaSyDzhwOOnCbpkuADs_T70SuocZ5ZmtNe4cY",
  authDomain: "textbook-platform.firebaseapp.com",
  projectId: "textbook-platform",
  storageBucket: "textbook-platform.firebasestorage.app",
  messagingSenderId: "376480732351",
  appId: "1:376480732351:web:95a0cd1e6837c0c3d2220a"
};

// Initialize Firebase
const app = initializeApp(firebaseConfig);
const auth = getAuth(app);
const db = getFirestore(app);

async function createAdminUser() {
  try {
    console.log('Creating admin user...');
    
    // Create the admin user
    const userCredential = await createUserWithEmailAndPassword(
      auth, 
      '<EMAIL>', // Change this to your email
      'admin123456' // Change this to a secure password
    );
    
    const user = userCredential.user;
    console.log('Firebase user created:', user.uid);
    
    // Update display name
    await updateProfile(user, {
      displayName: 'Administrator'
    });
    
    // Create user profile in Firestore
    const userProfile = {
      uid: user.uid,
      email: '<EMAIL>',
      username: 'Administrator',
      role: 'admin',
      createdAt: Date.now()
    };
    
    await setDoc(doc(db, 'users', user.uid), userProfile);
    console.log('User profile created in Firestore');
    
    console.log('✅ Admin user created successfully!');
    console.log('Email: <EMAIL>');
    console.log('Password: admin123456');
    console.log('⚠️  Please change the password after first login!');
    
    process.exit(0);
  } catch (error) {
    console.error('❌ Error creating admin user:', error.message);
    process.exit(1);
  }
}

createAdminUser();

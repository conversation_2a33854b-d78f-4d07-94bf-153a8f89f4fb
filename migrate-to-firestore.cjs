const admin = require('firebase-admin');
const fs = require('fs').promises;
const path = require('path');

// Initialize Firebase Admin
const firebaseConfig = {
  projectId: "textbook-platform",
};

admin.initializeApp({
  projectId: firebaseConfig.projectId,
});

const db = admin.firestore();

async function migrateBooks() {
  try {
    console.log('🔄 Starting migration from books.json to Firestore...');
    
    // Read the existing books.json file
    const booksPath = path.join(__dirname, 'public', 'books.json');
    const booksData = await fs.readFile(booksPath, 'utf8');
    const books = JSON.parse(booksData);
    
    console.log(`📚 Found ${books.length} books to migrate`);
    
    // Migrate each book to Firestore
    for (const book of books) {
      console.log(`📖 Migrating book: ${book.title}`);
      
      // Create book document
      const bookRef = db.collection('books').doc(book.id);
      await bookRef.set({
        id: book.id,
        title: book.title,
        description: book.description,
        createdAt: admin.firestore.FieldValue.serverTimestamp(),
        updatedAt: admin.firestore.FieldValue.serverTimestamp()
      });
      
      // Migrate chapters
      if (book.chapters && book.chapters.length > 0) {
        console.log(`  📑 Migrating ${book.chapters.length} chapters`);
        
        for (const chapter of book.chapters) {
          const chapterRef = bookRef.collection('chapters').doc(chapter.id);
          await chapterRef.set({
            id: chapter.id,
            title: chapter.title,
            createdAt: admin.firestore.FieldValue.serverTimestamp(),
            updatedAt: admin.firestore.FieldValue.serverTimestamp()
          });
          
          // Migrate items
          if (chapter.items && chapter.items.length > 0) {
            console.log(`    📄 Migrating ${chapter.items.length} items`);
            
            for (const item of chapter.items) {
              const itemRef = chapterRef.collection('items').doc(item.id);
              await itemRef.set({
                ...item,
                createdAt: admin.firestore.FieldValue.serverTimestamp(),
                updatedAt: admin.firestore.FieldValue.serverTimestamp()
              });
            }
          }
        }
      }
    }
    
    console.log('✅ Migration completed successfully!');
    console.log('📝 You can now update your server to use Firestore instead of books.json');
    
  } catch (error) {
    console.error('❌ Migration failed:', error);
    process.exit(1);
  }
}

// Run migration
migrateBooks().then(() => {
  console.log('🎉 All done!');
  process.exit(0);
});
